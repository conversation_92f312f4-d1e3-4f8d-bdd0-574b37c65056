import { useState, useEffect } from 'react';
import { personnelService, referenceService, badgeService } from '../services/apiService';
import { Alert } from './ui';

export function PersonnelForm({ personnel, onSuccess, onCancel }) {
  const [formData, setFormData] = useState({
    type: 'militaire_interne',
    nom: '',
    prenom: '',
    matricule: '',
    cin: '',
    grade: '',
    fonction: '',
    societe: '',
    unite: '',
    destination: '',
    unite_origine: '',
    heure_entree: '',
    badge_id: '',
    objet_visite: ''
  });
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [grades, setGrades] = useState([]);
  const [unites, setUnites] = useState([]);
  const [badgesDisponibles, setBadgesDisponibles] = useState([]);

  useEffect(() => {
    // Charger les données de référence
    const fetchReferenceData = async () => {
      try {
        const [gradesData, unitesData, badgesData] = await Promise.all([
          referenceService.getGrades(),
          referenceService.getUnites(),
          badgeService.getAll({ statut: 'disponible' })
        ]);
        setGrades(gradesData);
        setUnites(unitesData);
        setBadgesDisponibles(badgesData);
      } catch (err) {
        console.error('Error fetching reference data:', err);
      }
    };

    fetchReferenceData();

    // Pré-remplir le formulaire si on modifie un personnel existant
    if (personnel) {
      setFormData({
        type: personnel.type,
        nom: personnel.nom || '',
        prenom: personnel.prenom || '',
        matricule: personnel.matricule || '',
        cin: personnel.cin || '',
        grade: personnel.grade || '',
        fonction: personnel.fonction || '',
        societe: personnel.societe || '',
        unite: personnel.unite || '',
        destination: personnel.destination || '',
        unite_origine: personnel.unite_origine || '',
        heure_entree: personnel.heure_entree || '',
        badge_id: personnel.badge_id || '',
        objet_visite: personnel.objet_visite || ''
      });
    }
  }, [personnel]);

  const handleChange = (e) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleSubmit = async (e) => {
    e.preventDefault();
    setLoading(true);
    setError(null);

    try {
      if (personnel) {
        await personnelService.update(personnel.id, formData);
      } else {
        await personnelService.create(formData);
      }
      onSuccess();
    } catch (err) {
      setError(err.response?.data?.message || 'Erreur lors de la sauvegarde');
    } finally {
      setLoading(false);
    }
  };

  const renderMilitaireInterneFields = () => (
    <>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Matricule *
          </label>
          <input
            type="text"
            name="matricule"
            value={formData.matricule}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Grade *
          </label>
          <select
            name="grade"
            value={formData.grade}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Sélectionner un grade</option>
            {grades.map(grade => (
              <option key={grade.id} value={grade.id}>{grade.nom}</option>
            ))}
          </select>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Unité *
        </label>
        <select
          name="unite"
          value={formData.unite}
          onChange={handleChange}
          required
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="">Sélectionner une unité</option>
          {unites.map(unite => (
            <option key={unite.id} value={unite.id}>{unite.nom}</option>
          ))}
        </select>
      </div>
    </>
  );

  const renderVisiteurFields = () => (
    <>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            CIN *
          </label>
          <input
            type="text"
            name="cin"
            value={formData.cin}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        {formData.type === 'militaire_externe' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Grade *
            </label>
            <select
              name="grade"
              value={formData.grade}
              onChange={handleChange}
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            >
              <option value="">Sélectionner un grade</option>
              {grades.map(grade => (
                <option key={grade.id} value={grade.id}>{grade.nom}</option>
              ))}
            </select>
          </div>
        )}
        {formData.type === 'civil_externe' && (
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-1">
              Société *
            </label>
            <input
              type="text"
              name="societe"
              value={formData.societe}
              onChange={handleChange}
              required
              className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
            />
          </div>
        )}
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Destination *
          </label>
          <select
            name="destination"
            value={formData.destination}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Sélectionner une destination</option>
            {unites.map(unite => (
              <option key={unite.id} value={unite.id}>{unite.nom}</option>
            ))}
          </select>
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Unité d'origine *
          </label>
          <select
            name="unite_origine"
            value={formData.unite_origine}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Sélectionner une unité d'origine</option>
            {unites.map(unite => (
              <option key={unite.id} value={unite.id}>{unite.nom}</option>
            ))}
          </select>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Heure d'entrée
          </label>
          <input
            type="datetime-local"
            name="heure_entree"
            value={formData.heure_entree}
            onChange={handleChange}
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Badge visiteur *
          </label>
          <select
            name="badge_id"
            value={formData.badge_id}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          >
            <option value="">Sélectionner un badge</option>
            {badgesDisponibles.filter(badge => badge.type === 'visiteur').map(badge => (
              <option key={badge.id} value={badge.id}>Badge #{badge.numero}</option>
            ))}
          </select>
        </div>
      </div>
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Objet de la visite *
        </label>
        <textarea
          name="objet_visite"
          value={formData.objet_visite}
          onChange={handleChange}
          required
          rows={3}
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          placeholder="Décrivez l'objet de la visite..."
        />
      </div>
    </>
  );

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      {error && (
        <Alert variant="error" dismissible onDismiss={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Type de personnel */}
      <div>
        <label className="block text-sm font-medium text-gray-700 mb-1">
          Type de personnel *
        </label>
        <select
          name="type"
          value={formData.type}
          onChange={handleChange}
          required
          className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
        >
          <option value="militaire_interne">Militaire Interne</option>
          <option value="militaire_externe">Militaire Externe (Visiteur)</option>
          <option value="civil_externe">Civil Externe (Visiteur)</option>
        </select>
      </div>

      {/* Informations de base */}
      <div className="grid grid-cols-2 gap-4">
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Nom *
          </label>
          <input
            type="text"
            name="nom"
            value={formData.nom}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
        <div>
          <label className="block text-sm font-medium text-gray-700 mb-1">
            Prénom *
          </label>
          <input
            type="text"
            name="prenom"
            value={formData.prenom}
            onChange={handleChange}
            required
            className="w-full border border-gray-300 rounded-md px-3 py-2 focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
          />
        </div>
      </div>

      {/* Champs spécifiques selon le type */}
      {formData.type === 'militaire_interne' && renderMilitaireInterneFields()}
      {(formData.type === 'militaire_externe' || formData.type === 'civil_externe') && renderVisiteurFields()}

      {/* Boutons */}
      <div className="flex justify-end space-x-3 pt-4 border-t border-gray-200">
        <button
          type="button"
          onClick={onCancel}
          className="px-4 py-2 text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
        >
          Annuler
        </button>
        <button
          type="submit"
          disabled={loading}
          className="px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 disabled:opacity-50"
        >
          {loading ? 'Sauvegarde...' : (personnel ? 'Modifier' : 'Créer')}
        </button>
      </div>
    </form>
  );
}
