// Service d'adaptation des données entre frontend et backend

/**
 * Adapte les données de personnel du backend vers le frontend
 */
export const adaptPersonnelFromBackend = (personnelData) => {
  if (!personnelData) return null;

  return {
    id: personnelData.id,
    nom: personnelData.nom,
    prenom: personnelData.prenom,
    type: personnelData.typePersonnel?.nom_type || personnelData.TypePersonnel?.nom_type || personnelData.type_personnel,
    matricule: personnelData.matricule,
    cin: personnelData.cin,
    grade: personnelData.grade?.nom_grade || personnelData.Grade?.nom_grade,
    unite: personnelData.unite?.nom_unite || personnelData.Unite?.nom_unite,
    fonction: personnelData.civilInfo?.fonction || personnelData.fonction,
    destination: personnelData.militaireExterneInfo?.destination || personnelData.civilInfo?.destination || personnelData.destination,
    unite_origine: personnelData.militaireExterneInfo?.unite_origine || personnelData.civilInfo?.unite_origine || personnelData.unite_origine,
    heure_entree: personnelData.militaireExterneInfo?.heure_entree || personnelData.civilInfo?.heure_entree || personnelData.heure_entree,
    badge_id: personnelData.badge?.id,
    badge_numero: personnelData.badge?.epc_code,
    date_creation: personnelData.created_at,
    date_modification: personnelData.updated_at
  };
};

/**
 * Adapte les données de personnel du frontend vers le backend
 */
export const adaptPersonnelToBackend = (personnelData) => {
  // Fonction utilitaire pour convertir en entier
  const toInt = (value) => {
    if (value === null || value === undefined || value === '') return null;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? null : parsed;
  };

  // Fonction utilitaire pour formater la date
  const formatDate = (dateValue) => {
    if (!dateValue) return null;
    if (dateValue instanceof Date) return dateValue.toISOString();
    if (typeof dateValue === 'string') {
      const date = new Date(dateValue);
      return isNaN(date.getTime()) ? null : date.toISOString();
    }
    return null;
  };

  const baseData = {
    nom: personnelData.nom,
    prenom: personnelData.prenom
  };

  // Ajouter les champs spécifiques selon le type
  if (personnelData.type === 'militaire_interne') {
    return {
      ...baseData,
      matricule: personnelData.matricule,
      id_grade: toInt(personnelData.grade),
      id_unite: toInt(personnelData.unite)
    };
  } else if (personnelData.type === 'militaire_externe') {
    return {
      ...baseData,
      matricule: personnelData.matricule,
      cin: personnelData.cin,
      id_grade: toInt(personnelData.grade),
      horaire_entree: formatDate(personnelData.heure_entree),
      id_unite_origine: toInt(personnelData.unite_origine),
      destination: toInt(personnelData.destination),
      objet_visite: personnelData.objet_visite || '',
      id_badge: toInt(personnelData.badge_id)
    };
  } else if (personnelData.type === 'civil_externe') {
    return {
      ...baseData,
      cin: personnelData.cin,
      societe: personnelData.societe || personnelData.fonction || '',
      horaire_entree: formatDate(personnelData.heure_entree),
      destination: toInt(personnelData.destination),
      objet_visite: personnelData.objet_visite || '',
      id_badge: toInt(personnelData.badge_id)
    };
  }

  return baseData;
};

/**
 * Valide les données de personnel avant envoi au backend
 */
export const validatePersonnelData = (personnelData) => {
  const errors = [];

  // Validation des champs obligatoires
  if (!personnelData.nom || personnelData.nom.trim() === '') {
    errors.push('Le nom est obligatoire');
  }
  if (!personnelData.prenom || personnelData.prenom.trim() === '') {
    errors.push('Le prénom est obligatoire');
  }
  if (!personnelData.type) {
    errors.push('Le type de personnel est obligatoire');
  }

  // Validation spécifique selon le type
  if (personnelData.type === 'militaire_interne') {
    if (!personnelData.matricule || personnelData.matricule.trim() === '') {
      errors.push('Le matricule est obligatoire pour un militaire interne');
    }
    if (!personnelData.grade) {
      errors.push('Le grade est obligatoire pour un militaire interne');
    }
    if (!personnelData.unite) {
      errors.push('L\'unité est obligatoire pour un militaire interne');
    }
  } else if (personnelData.type === 'militaire_externe') {
    if (!personnelData.matricule || personnelData.matricule.trim() === '') {
      errors.push('Le matricule est obligatoire pour un militaire externe');
    }
    if (!personnelData.cin || personnelData.cin.trim() === '') {
      errors.push('Le CIN est obligatoire pour un militaire externe');
    }
    if (!personnelData.grade) {
      errors.push('Le grade est obligatoire pour un militaire externe');
    }
    if (!personnelData.heure_entree) {
      errors.push('L\'heure d\'entrée est obligatoire pour un militaire externe');
    }
    if (!personnelData.unite_origine) {
      errors.push('L\'unité d\'origine est obligatoire pour un militaire externe');
    }
    if (!personnelData.destination) {
      errors.push('La destination est obligatoire pour un militaire externe');
    }
    if (!personnelData.badge_id) {
      errors.push('Le badge est obligatoire pour un militaire externe');
    }
  } else if (personnelData.type === 'civil_externe') {
    if (!personnelData.cin || personnelData.cin.trim() === '') {
      errors.push('Le CIN est obligatoire pour un civil externe');
    }
    if (!personnelData.heure_entree) {
      errors.push('L\'heure d\'entrée est obligatoire pour un civil externe');
    }
    if (!personnelData.destination) {
      errors.push('La destination est obligatoire pour un civil externe');
    }
    if (!personnelData.badge_id) {
      errors.push('Le badge est obligatoire pour un civil externe');
    }
  }

  // Validation des ID (doivent être des entiers valides)
  const idFields = ['grade', 'unite', 'unite_origine', 'destination', 'badge_id'];
  idFields.forEach(field => {
    if (personnelData[field] && personnelData[field] !== '') {
      const value = parseInt(personnelData[field], 10);
      if (isNaN(value) || value <= 0) {
        errors.push(`${field} doit être un identifiant valide`);
      }
    }
  });

  return {
    isValid: errors.length === 0,
    errors
  };
};

const BADGE_TYPE_LABELS = {
  1: 'badge_visiteur_militaire',
  2: 'badge_visiteur_civil'
};

/**
 * Adapte les données de badge du backend vers le frontend
 */
export const adaptBadgeFromBackend = (badgeData) => {
  if (!badgeData) return null;

  return {
    id: badgeData.id,
    numero: badgeData.epc_code,
    epc_code: badgeData.epc_code,
    type:
      badgeData.TypeBadge?.nom_type_badge ||
      BADGE_TYPE_LABELS[badgeData.id_type_badge || badgeData.type_id] ||
      badgeData.type,
    statut: badgeData.actif ? (badgeData.permanent ? 'actif' : 'disponible') : 'desactive',
    date_creation: badgeData.created_at,
    date_expiration: badgeData.date_expiration,
    description: badgeData.description,
    personnel: badgeData.Personnel ? adaptPersonnelFromBackend(badgeData.Personnel) : null
  };
};

/**
 * Adapte les données de badge du frontend vers le backend
 */
export const adaptBadgeToBackend = (badgeData, isCreation = false) => {
  // Fonction utilitaire pour convertir en entier
  const toInt = (value) => {
    if (value === null || value === undefined || value === '') return null;
    const parsed = parseInt(value, 10);
    return isNaN(parsed) ? null : parsed;
  };

  if (isCreation) {
    // Pour la création, le backend attend type_badge (string)
    return {
      type_badge: badgeData.type // Le type de badge comme string
    };
  } else {
    // Pour la mise à jour, le backend attend les champs complets
    return {
      epc_code: badgeData.numero, // Le numéro de badge correspond à epc_code
      id_type_badge: toInt(badgeData.type), // Convertir le type en ID entier
      actif: badgeData.statut === 'actif', // Convertir le statut en booléen
      permanent: badgeData.permanent || false,
      description: badgeData.description
    };
  }
};

/**
 * Valide les données de badge avant envoi au backend
 */
export const validateBadgeData = (badgeData, isCreation = false) => {
  const errors = [];

  if (isCreation) {
    // Validation pour la création
    if (!badgeData.type) {
      errors.push('Le type de badge est obligatoire');
    } else {
      const validTypes = ['badge_visiteur_militaire', 'badge_visiteur_civil'];
      if (!validTypes.includes(badgeData.type)) {
        errors.push('Type de badge invalide. Types valides: ' + validTypes.join(', '));
      }
    }
  } else {
    // Validation pour la mise à jour
    if (!badgeData.numero || badgeData.numero.trim() === '') {
      errors.push('Le numéro de badge est obligatoire');
    }
    if (!badgeData.type) {
      errors.push('Le type de badge est obligatoire');
    }
    if (!badgeData.statut) {
      errors.push('Le statut du badge est obligatoire');
    } else {
      const validStatuts = ['actif', 'desactive'];
      if (!validStatuts.includes(badgeData.statut)) {
        errors.push('Statut invalide. Statuts valides: ' + validStatuts.join(', '));
      }
    }

    // Validation de l'ID du type de badge (doit être un entier valide)
    if (badgeData.type && badgeData.type !== '') {
      const value = parseInt(badgeData.type, 10);
      if (isNaN(value) || value <= 0) {
        errors.push('Le type de badge doit être un identifiant valide');
      }
    }
  }

  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Adapte les données de passage du backend vers le frontend
 */
export const adaptPassageFromBackend = (passageData) => {
  if (!passageData) return null;
  
  return {
    id: passageData.id,
    date_passage: passageData.date_passage,
    type_acces: passageData.type_acces,
    resultat: passageData.resultat,
    motif_refus: passageData.motif_refus,
    badge_numero: passageData.badge?.numero,
    porte_nom: passageData.porte?.nom,
    personnel: passageData.personnel ? adaptPersonnelFromBackend(passageData.personnel) : null
  };
};

/**
 * Adapte les données d'attribution du backend vers le frontend
 */
export const adaptAttributionFromBackend = (attributionData) => {
  if (!attributionData) return null;

  return {
    id: attributionData.id,
    badge_id: attributionData.id_badge,
    badge_numero: attributionData.badge?.epc_code,
    badge_type: attributionData.badge?.TypeBadge?.nom_type_badge,
    personnel_id: attributionData.id_personnel,
    personnel_nom: attributionData.personnel?.nom,
    personnel_prenom: attributionData.personnel?.prenom,
    personnel_type: attributionData.personnel?.typePersonnel?.nom_type,
    personnel_matricule: attributionData.personnel?.matricule,
    personnel_cin: attributionData.personnel?.cin,
    date_attribution: attributionData.date_attribution,
    date_expiration: attributionData.date_expiration,
    date_fin: attributionData.date_fin,
    statut: attributionData.statut,
    destination: attributionData.personnel?.militaireExterneInfo?.destination || attributionData.personnel?.civilInfo?.destination,
    unite: attributionData.personnel?.unite?.nom_unite
  };
};

/**
 * Adapte les données de référence du backend vers le frontend
 */
export const adaptReferenceFromBackend = (referenceData, type) => {
  if (!referenceData) return null;

  const baseData = {
    id: referenceData.id,
    description: referenceData.description,
    actif: referenceData.actif !== false // Par défaut true si pas défini
  };

  switch (type) {
    case 'grade':
      return {
        ...baseData,
        nom: referenceData.nom_grade,
        niveau: referenceData.niveau
      };
    case 'unite':
      return {
        ...baseData,
        nom: referenceData.nom_unite,
        code: referenceData.code_unite
      };
    case 'porte':
      return {
        ...baseData,
        nom: referenceData.libelle,
        localisation: referenceData.localisation,
        type_acces: referenceData.type_acces
      };
    case 'type_badge':
      return {
        ...baseData,
        nom: referenceData.nom_type_badge,
        couleur: referenceData.couleur,
        duree_validite: referenceData.duree_validite_heures
      };
    default:
      return {
        ...baseData,
        nom: referenceData.nom || referenceData.libelle || referenceData.nom_type
      };
  }
};

/**
 * Adapte les données de référence du frontend vers le backend
 */
export const adaptReferenceToBackend = (referenceData, type) => {
  const baseData = {
    nom: referenceData.nom,
    description: referenceData.description
  };

  switch (type) {
    case 'grade':
      return {
        ...baseData,
        niveau: referenceData.niveau
      };
    case 'unite':
      return {
        ...baseData,
        code: referenceData.code
      };
    case 'porte':
      return {
        ...baseData,
        localisation: referenceData.localisation,
        type_acces: referenceData.type_acces
      };
    case 'type_badge':
      return {
        ...baseData,
        couleur: referenceData.couleur,
        duree_validite_heures: referenceData.duree_validite
      };
    default:
      return baseData;
  }
};

/**
 * Adapte les statistiques du backend vers le frontend
 */
export const adaptStatisticsFromBackend = (statsData) => {
  if (!statsData) {
    return {
      passagesParJour: [],
      passagesParHeure: [],
      passagesParPorte: [],
      passagesParType: [],
      tendances: {},
      resume: {
        totalPassages: 0,
        passagesAutorises: 0,
        tauxAutorisation: 0,
        heurePointe: '--:--',
        passagesHeurePointe: 0,
        badgesActifs: 0,
        badgesVisiteurs: 0
      }
    };
  }

  return {
    passagesParJour: Array.isArray(statsData.passages_par_jour) ? statsData.passages_par_jour : [],
    passagesParHeure: Array.isArray(statsData.passages_par_heure) ? statsData.passages_par_heure : [],
    passagesParPorte: Array.isArray(statsData.passages_par_porte) ? statsData.passages_par_porte : [],
    passagesParType: Array.isArray(statsData.passages_par_type) ? statsData.passages_par_type : [],
    tendances: statsData.tendances || {},
    resume: {
      totalPassages: statsData.total_passages || 0,
      passagesAutorises: statsData.passages_autorises || 0,
      tauxAutorisation: statsData.taux_autorisation || 0,
      heurePointe: statsData.heure_pointe || '--:--',
      passagesHeurePointe: statsData.passages_heure_pointe || 0,
      badgesActifs: statsData.badges_actifs || 0,
      badgesVisiteurs: statsData.badges_visiteurs || 0
    }
  };
};
